/* /src/index.css */

@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@100..900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-hide {
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
  }
}

body {
  margin: 0;
  font-family: "Noto Sans KR", sans-serif; /* 여기에 폰트 적용 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.modal-open header {
  /* backdrop-filter와 background-color를 제거합니다. */
  backdrop-filter: none !important;
  background-color: transparent !important; /* 또는 원하는 다른 색상 */
  box-shadow: none !important; /* 그림자도 제거하고 싶다면 */
}

.modal-open header button {
  /* 애초에 마우스 이벤트를 받지 않도록 합니다. */
  pointer-events: none !important;
}
