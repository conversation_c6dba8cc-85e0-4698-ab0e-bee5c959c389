export interface CardProps {
  iconUrl: string
  title: string
  description: string
  buttonText: string
  bgColor?: string
  textColorClass?: string
  descriptionColorClass?: string
  buttonClasses?: string
}

export interface AddCardButtonProps {
  onClick: () => void
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
}

export interface PortalProps {
  children: React.ReactNode
}

export interface TiptapEditorProps {
  content: string
  editable: boolean
  onUpdate?: (content: string) => void
}
