// tailwind.config.js

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      typography: ({ theme }) => ({
        DEFAULT: {
          css: {
            maxWidth: "none",
            strong: {
              color: "inherit",
              fontWeight: theme("fontWeight.bold"),
            },

            h1: {
              fontWeight: theme("fontWeight.bold"),
              marginTop: theme("spacing.8"),
              marginBottom: theme("spacing.4"),
            },
            h2: {
              fontWeight: theme("fontWeight.bold"),
              marginTop: theme("spacing.6"),
              marginBottom: theme("spacing.3"),
            },
            h3: {
              fontWeight: theme("fontWeight.bold"),
              marginTop: theme("spacing.5"),
              marginBottom: theme("spacing.2"),
            },
            p: {
              marginTop: theme("spacing.0"),
              marginBottom: theme("spacing.0"),
            },
            a: {
              color: theme("colors.blue.500"),
              "&:hover": {
                color: theme("colors.blue.700"),
              },
              cursor: "pointer",
            },
            ul: {
              marginTop: theme("spacing.0"), // 변경
              marginBottom: theme("spacing.0"), // 변경
              paddingLeft: theme("spacing.5"),
              listStyleType: "disc",
            },
            ol: {
              marginTop: theme("spacing.0"),
              marginBottom: theme("spacing.0"),
              paddingLeft: theme("spacing.5"),
              listStyleType: "decimal",
            },
            "li::marker": {
              color: theme("colors.gray.800"),
            },
            li: {
              marginTop: theme("spacing.0"),
              marginBottom: theme("spacing.0"),
              marginLeft: theme("spacing.4"),
              p: {
                marginTop: theme("spacing.0"),
                marginBottom: theme("spacing.0"),
              },
            },
            // TipTap이 생성하는 비어있는 p 태그의 기본 높이를 없애 깔끔하게 보이도록 함
            "p.is-editor-empty:first-child::before": {
              content: "none",
            },
          },
        },
      }),
    },
  },
  plugins: [require("@tailwindcss/typography")],
}
