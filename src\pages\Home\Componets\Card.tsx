import CardComponent from "@/components/common/CardComponent"
import google from "@/assets/images/google.svg"
import ondedrive from "@/assets/images/onedrive.svg"
import { AddCardButtonComponent } from "@/components/common"

const cardData = [
  {
    id: 1,
    iconUrl: google, // 예시 아이콘 URL
    title: "<PERSON><PERSON><PERSON>",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.",
    buttonText: "View Project",
    bgColor: "#1F2937", // 다크 배경
    textColorClass: "text-white",
    descriptionColorClass: "text-gray-400",
    buttonClasses: "bg-gray-700 text-white hover:bg-gray-600",
  },
  {
    id: 2,
    iconUrl: ondedrive, // 예시 아이콘 URL
    title: "Maye Project",
    description: "Lime iploe dolor sit, tamposiclor elit, quico relforeseenes pepisovs.",
    buttonText: "View Project",
    bgColor: "white", // 라이트 배경
    textColorClass: "text-gray-900",
    descriptionColorClass: "text-gray-500",
    buttonClasses: "border border-gray-300 text-gray-700 hover:bg-gray-50",
  },
]

const Card = () => {
  const handleAddNewCard = () => {
    console.log("새로운 카드를 추가합니다!")
    // 여기에 새로운 카드를 cardData 상태에 추가하는 로직을 구현하면 됩니다.
    // 예: setCardData([...cardData, newCardObject]);
  }

  return (
    <div
      id="card"
      className="grid grid-cols-1 xl:grid-cols-2 gap-6 max-h-[800px] overflow-y-auto scrollbar-hide"
    >
      {cardData.map((item) => (
        <CardComponent
          key={item.id}
          iconUrl={item.iconUrl}
          title={item.title}
          description={item.description}
          buttonText={item.buttonText}
          bgColor={item.bgColor}
          textColorClass={item.textColorClass}
          descriptionColorClass={item.descriptionColorClass}
          buttonClasses={item.buttonClasses}
        />
      ))}

      <AddCardButtonComponent onClick={handleAddNewCard} />
    </div>
  )
}

export default Card
