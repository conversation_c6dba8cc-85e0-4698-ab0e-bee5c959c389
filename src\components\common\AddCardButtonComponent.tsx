import type { AddCardButtonProps } from "@/types/common.types"

const AddCardButtonComponent = ({ onClick }: AddCardButtonProps) => {
  return (
    <div
      onClick={onClick}
      className="flex items-center justify-center p-6 rounded-2xl cursor-pointer 
                 border-2 border-dashed border-gray-300 hover:bg-gray-100
                 transition-colors min-h-[220px]"
    >
      <span className="text-5xl text-gray-400">+</span>
    </div>
  )
}

export default AddCardButtonComponent
