import { useState } from "react"
import LoginModal from "@/components/common/LoginModalComponent"

const Header = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <header className="flex justify-between items-center sticky top-0 z-50 py-4 px-36 bg-white/80 backdrop-blur-sm shadow-sm">
      <div className="text-xl font-bold">
        <a href="/">Developer's Blog</a>
      </div>
      <nav>
        <ul className="flex space-x-6">
          <li>
            <button
              onClick={() => setIsModalOpen(true)}
              className="px-4 py-2 font-medium shadow-sm border border-gray-400 rounded-lg hover:bg-gray-100 transition-colors"
            >
              로그인
            </button>
          </li>

          <LoginModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
        </ul>
      </nav>
    </header>
  )
}

export default Header
