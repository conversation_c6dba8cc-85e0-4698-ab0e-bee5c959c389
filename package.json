{"name": "my-blog-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tiptap/extension-color": "^3.4.1", "@tiptap/extension-link": "^3.4.1", "@tiptap/extension-text-align": "^3.4.1", "@tiptap/extension-text-style": "^3.4.1", "@tiptap/extension-underline": "^3.4.1", "@tiptap/react": "^3.4.1", "@tiptap/starter-kit": "^3.4.1", "@vitejs/plugin-react": "^5.0.2", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.8.2", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.3.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vite-plugin-html-env": "^1.2.8", "vite-tsconfig-paths": "^5.1.4"}}