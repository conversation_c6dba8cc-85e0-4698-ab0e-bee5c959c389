import { useEffect, useState } from "react"
import { createPortal } from "react-dom"
import type { PortalProps } from "@/types/common.types"

const PortalComponent = ({ children }: PortalProps) => {
  const [element, setElement] = useState<HTMLElement | null>(null)

  useEffect(() => {
    setElement(document.getElementById("modal-root"))
  }, [])

  if (!element) {
    return null
  }

  return createPortal(children, element)
}

export default PortalComponent
