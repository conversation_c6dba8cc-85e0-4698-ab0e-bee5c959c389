import { ServiceEnvType, ServiceEnvConfig } from "./src/types/env"

type ServiceEnv = Record<ServiceEnvType, ServiceEnvConfig>

const serviceEnv: ServiceEnv = {
  dev: { serverURL: "http://localhost:8080/api" },
  prod: { serverURL: "https://localhost:8080/api" },
}

export function getServiceEnvConfig(env: ImportMetaEnv): ServiceEnvConfig {
  const { VITE_SERVICE_ENV = "dev" } = env

  const config = serviceEnv[VITE_SERVICE_ENV]

  return {
    ...config,
  }
}
