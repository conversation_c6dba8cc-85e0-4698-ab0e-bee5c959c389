import { useEffect } from "react"
import { IoClose } from "react-icons/io5"
import PortalComponent from "./PotalComponent"
import LoginFormComponent from "./LoginFormComponent"
import type { ModalProps } from "@/types/common.types"

const Modal = ({ isOpen, onClose }: ModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open")
    } else {
      document.body.classList.remove("modal-open")
    }

    return () => {
      document.body.classList.remove("modal-open")
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <PortalComponent>
      <div onClick={onClose} className="fixed inset-0 bg-black/50 flex justify-center items-center">
        <div
          onClick={(e) => e.stopPropagation()}
          className="bg-white rounded-lg shadow-xl p-5 relative"
        >
          <button onClick={onClose} className="absolute top-2 right-2">
            <IoClose size={24} />
          </button>
          <LoginFormComponent />
        </div>
      </div>
    </PortalComponent>
  )
}

export default Modal
