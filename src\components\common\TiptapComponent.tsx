import { EditorContent, useEditor, useEditorState } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Underline from "@tiptap/extension-underline"
import Link from "@tiptap/extension-link"
import React from "react"
import { TextStyle } from "@tiptap/extension-text-style"
import { Color } from "@tiptap/extension-color"
import { BubbleMenu } from "@tiptap/react/menus"
import type { TiptapEditorProps } from "@/types/common.types"

const TiptapComponent = ({ content, editable, onUpdate }: TiptapEditorProps) => {
  const editor = useEditor({
    extensions: [StarterKit, TextStyle, Color, Underline, Link.configure({ openOnClick: false })],
    editable: editable,
    content: content,
    editorProps: {
      attributes: {
        class: "focus:outline-none prose max-w-none",
      },
    },
    onUpdate: ({ editor }) => {
      onUpdate?.(editor.getHTML())
    },
  })

  const setLink = React.useCallback(() => {
    if (!editor) return
    const previousUrl = editor.getAttributes("link").href
    const url = window.prompt("URL을 입력하세요", previousUrl)
    if (url === null) return
    if (url === "") {
      editor.chain().focus().extendMarkRange("link").unsetLink().run()
      return
    }
    editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run()
  }, [editor])

  const editorState = useEditorState({
    editor,
    selector: (ctx) => {
      return {
        color: ctx.editor.getAttributes("textStyle").color,
      }
    },
  })

  if (!editor) {
    return null
  }

  // 버튼 스타일을 위한 공통 클래스
  const buttonClass = "px-3 py-2 text-sm hover:bg-gray-100"

  return (
    <div className="pl-4 w-full">
      <BubbleMenu
        editor={editor}
        className="bg-white border border-gray-200 rounded-lg shadow-xl flex items-center"
      >
        {/* 그룹 1: 텍스트 스타일 */}
        <div className="flex items-center">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`${buttonClass} font-bold ${editor.isActive("bold")}`}
          >
            B
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`${buttonClass} italic ${editor.isActive("italic")}`}
          >
            I
          </button>
          <button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={`${buttonClass} underline ${editor.isActive("underline")}`}
          >
            U
          </button>
        </div>

        <div className="w-px h-5 bg-gray-300"></div>

        {/* 그룹 2: 문단 종류 */}
        <div className="flex items-center">
          <button
            onClick={() => editor.chain().focus().setParagraph().run()}
            className={`${buttonClass} ${editor.isActive("paragraph")}`}
          >
            P
          </button>
          <button
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            className={`${buttonClass} font-bold ${editor.isActive("heading", { level: 1 })}`}
          >
            H₁
          </button>
          <button
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={`${buttonClass} font-bold ${editor.isActive("heading", { level: 2 })}`}
          >
            H₂
          </button>
          <button
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            className={`${buttonClass} font-bold ${editor.isActive("heading", { level: 3 })}`}
          >
            H₃
          </button>
        </div>

        <div className="w-px h-5 bg-gray-300"></div>

        {/* 그룹 3: 링크, 색상 및 목록 */}
        <div className="flex items-center">
          <button onClick={setLink} className={`${buttonClass} ${editor.isActive("link")}`}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0m-4.242 6.828a2 2 0 010-2.828l3-3a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0z"
                clipRule="evenodd"
              />
              <path
                fillRule="evenodd"
                d="M5.172 7.172a2 2 0 010 2.828l3 3a2 2 0 012.828 0m-8.485-8.485a2 2 0 012.828 0l3 3a2 2 0 010 2.828m5.657 5.657a2 2 0 010 2.828l-3 3a2 2 0 01-2.828 0m8.485-8.485a2 2 0 010 2.828l-3 3a2 2 0 01-2.828 0"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <input
            type="color"
            onInput={(event) => editor.chain().focus().setColor(event.currentTarget.value).run()}
            value={editorState.color}
            className="w-8 h-8 p-1 border-none bg-transparent cursor-pointer"
            title="텍스트 색상 변경"
          />
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`${buttonClass} ${editor.isActive("bulletList")}`}
            title="글머리 기호 목록"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`${buttonClass} ${editor.isActive("orderedList")}`}
            title="숫자 목록"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5 4a1 1 0 00-2 0v1.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L5 5.586V4zM5 14a1 1 0 00-2 0v1.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L5 15.586V14zM10 4a1 1 0 100 2h8a1 1 0 100-2h-8zm0 6a1 1 0 100 2h8a1 1 0 100-2h-8z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </BubbleMenu>

      <div className={!editable ? "cursor-default" : "cursor-text"}>
        <EditorContent editor={editor} />
      </div>
    </div>
  )
}

export default TiptapComponent
