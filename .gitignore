# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.*
!.env.example

# Dependency directories
node_modules/

# Build outputs
dist
dist-ssr

# Vite cache
.vite/

# Test coverage reports
coverage/

# Linters cache
.eslintcache

# TypeScript build cache
*.tsbuildinfo

# Local files
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?