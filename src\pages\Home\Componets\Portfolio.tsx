import { useRef, useState } from "react"

// 1. 포트폴리오 데이터를 UI와 분리하여 배열로 관리합니다.
const portfolioData = [
  {
    id: 1,
    imageUrl: "https://placehold.co/100",
    title: "Project Title",
    date: "2025.01 - 2025.03",
    description: "React와 TypeScript를 활용하여 제작한 개인 포트폴리오 사이트입니다.",
  },
  {
    id: 2,
    imageUrl: "https://placehold.co/100/3B82F6/FFFFFF",
    title: "Another Project",
    date: "2024.09 - 2024.12",
    description: "C#, ASP.NET MVC, MSSQL을 사용하여 개발한 사내 관리자용 웹 애플리케이션입니다.",
  },
]

const Portfolio = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const scrolled = scrollContainerRef.current.scrollTop > 0
      setIsScrolled(scrolled)
    }
  }

  return (
    <section id="portfolio" className="mt-24">
      <div
        className={`bg-white pt-4 transition-shadow duration-200 pb-1 ${
          isScrolled ? "shadow-[0_5px_5px_-5px_rgba(0,0,0,0.1)]" : ""
        }`}
      >
        <h3 className="text-2xl font-bold text-gray-800">Portfolio</h3>
      </div>
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="grid grid-cols-1 max-h-[500px] overflow-y-auto scrollbar-hide"
      >
        {portfolioData.map((item) => (
          <div id="card" className="flex items-start border-b border-gray-200 py-4 last:border-b-0">
            <img src={item.imageUrl} alt="Portfolio Item" className="w-24 h-24 rounded-lg mr-4" />
            <div>
              <h4 className="text-xl font-bold">{item.title}</h4>
              <p className="text-sm text-gray-500 mb-2">{item.date}</p>
              <p className="text-gray-700">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}

export default Portfolio
