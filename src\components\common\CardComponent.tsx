import type { CardProps } from "@/types/common.types"

const CardComponent = ({
  iconUrl,
  title,
  description,
  buttonText,
  bgColor = "#FFFFFF",
  textColorClass = "text-gray-800",
  descriptionColorClass = "text-gray-500",
  buttonClasses = "border border-gray-300 text-gray-700 hover:bg-gray-50",
}: CardProps) => {
  return (
    <div
      className="w-full min-h-[220px] rounded-2xl shadow-lg border border-gray-200 p-6 flex flex-col"
      style={{ backgroundColor: bgColor }}
    >
      {/* 아이콘 이미지를 URL로 받아서 처리 */}
      {iconUrl && <img src={iconUrl} alt={`${title} icon`} className="w-10 h-10 mb-2" />}

      <h3 className={`text-2xl font-bold mb-2 ${textColorClass}`}>{title}</h3>

      <p className={`text-sm mb-6 ${descriptionColorClass}`}>{description}</p>

      {/* 남은 공간을 모두 차지하여 하단 내용을 맨 밑으로 밀어냄 */}
      <div className="flex-grow"></div>

      {/* 카드 하단부 */}
      <div className="flex items-center justify-between">
        <button className={`px-5 py-2 rounded-full text-sm font-semibold ${buttonClasses}`}>
          {buttonText}
        </button>
      </div>
    </div>
  )
}

export default CardComponent
