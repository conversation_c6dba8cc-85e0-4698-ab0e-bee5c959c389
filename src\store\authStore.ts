import { create } from "zustand"

// 스토어의 상태(State) 타입을 정의합니다.
interface AuthState {
  accessToken: string | null
  isLoggedIn: boolean
}

// 스토어의 액션(Actions) 타입을 정의합니다.
interface AuthActions {
  setTokens: (accessToken: string) => void
  logout: () => void
}

// Zustand 스토어를 생성합니다.
export const useAuthStore = create<AuthState & AuthActions>((set) => ({
  // 초기 상태
  accessToken: null,
  isLoggedIn: false,

  // 액션 구현
  /**
   * 로그인 시 Access Token을 받아와 상태를 업데이트합니다.
   * @param accessToken
   */
  setTokens: (accessToken) =>
    set({
      accessToken: accessToken,
      isLoggedIn: true,
    }),

  /**
   * 로그아웃 시 모든 인증 관련 상태를 초기화합니다.
   */
  logout: () =>
    set({
      accessToken: null,
      isLoggedIn: false,
    }),
}))
