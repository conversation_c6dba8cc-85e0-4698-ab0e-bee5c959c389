import type { AxiosInstance, AxiosRequestConfig } from "axios"

// 커스텀 AxiosInstance 타입 정의
export default interface CustomAxiosInstance extends AxiosInstance {
  get<TResponse = any, TRequest = any>(
    url: string,
    config?: AxiosRequestConfig & { params?: TRequest },
  ): Promise<TResponse>

  post<TResponse = any, TRequest = any>(
    url: string,
    data?: TRequest,
    config?: AxiosRequestConfig,
  ): Promise<TResponse>

  put<TResponse = any, TRequest = any>(
    url: string,
    data?: TRequest,
    config?: AxiosRequestConfig,
  ): Promise<TResponse>

  delete<TResponse = any, TRequest = any>(
    url: string,
    config?: AxiosRequestConfig & { params?: TRequest },
  ): Promise<TResponse>
}
