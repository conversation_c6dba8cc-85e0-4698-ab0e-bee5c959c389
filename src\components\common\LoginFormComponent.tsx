import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aEyeSlash, FaUserCircle } from "react-icons/fa"
import { CiLock } from "react-icons/ci"
import { MdOutlineEmail } from "react-icons/md"
import { FcGoogle } from "react-icons/fc"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>wi<PERSON> } from "react-icons/fa6"
import { PiGithubLogoFill } from "react-icons/pi"

const LoginFormComponent = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault() // 페이지가 새로고침되는 것을 방지

    console.log("로그인 시도:", { email, password })
  }

  return (
    <form onSubmit={handleSubmit} className="w-80">
      {/* 타이틀 */}
      <div className="flex flex-col items-center justify-center mb-6 mt-4">
        <PiGithubLogoFill className=" h-10 w-10 text-gray-300 mb-4" />
        <h2 className="text-2xl font-bold">로그인</h2>
      </div>

      <div className="space-y-4">
        {/* 이메일 입력 필드 */}
        <div>
          <div className="mt-1 relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MdOutlineEmail className="h-5 w-5 text-gray-400" />
            </div>

            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder="이메일"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="appearance-none block w-full px-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        {/* 비밀번호 입력 필드 */}
        <div>
          <div className="mt-1 relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <CiLock className="h-5 w-5 text-gray-400" />
            </div>

            <input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autoComplete="current-password"
              required
              placeholder="비밀번호"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="appearance-none block w-full px-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
        </div>

        {/* 로그인 버튼 */}
        <div>
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600"
          >
            <FaUserCircle className="mr-2 h-5 w-5" /> 로그인
          </button>
        </div>

        {/* 구분선 */}
        <div className="relative flex items-center">
          <div className="flex-grow border-t border-gray-600"></div>
          <span className="mx-4 flex-shrink-0 text-sm text-gray-400">OR</span>
          <div className="flex-grow border-t border-gray-600"></div>
        </div>

        {/* 소셜 로그인 버튼 그룹 */}
        <div className="grid grid-cols-3 gap-3">
          <button
            type="button"
            className="inline-flex w-full items-center justify-center rounded-md border border-gray-400 bg-transparent p-2 hover:bg-gray-200"
          >
            <FaApple className="h-5 w-5" />
          </button>
          <button
            type="button"
            className="inline-flex w-full items-center justify-center rounded-md border border-gray-400 bg-transparent p-2 hover:bg-gray-200"
          >
            <FcGoogle className="h-5 w-5" />
          </button>
          <button
            type="button"
            className="inline-flex w-full items-center justify-center rounded-md border border-gray-400 bg-transparent p-2 hover:bg-gray-200"
          >
            <FaXTwitter className="h-5 w-5" />
          </button>
        </div>
      </div>
    </form>
  )
}

export default LoginFormComponent
